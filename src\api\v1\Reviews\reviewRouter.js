const express = require('express');
const router = express.Router();

const ReviewController = require('./reviewController');
const {
    validateReview,
    validateReviewUpdate,
    validateProviderResponse,
    validateHelpfulVote,
    validateReviewModeration,
    validateReviewQuery,
    validateRatingQuery,
    validateMultipleRatingQuery,
} = require('./reviewMiddleware');
const fetchAuth = require('../../common/utils/communicator');

// Create review (customers only)

router.post(
    '/',
    validateReview,
    fetchAuth.fetchAuthAllDataMiddleware,
    ReviewController.createReview
);

// Get reviews with filtering and pagination (public)
router.get('/', //validateReviewQuery,
     ReviewController.getReviews);

// Get reviews with enhanced frontend data (public)
router.get('/frontend', ReviewController.getReviewsForFrontend);

// Get review analytics (public)
router.get('/analytics', ReviewController.getReviewAnalytics);

// Get overall rating summary (public)
router.get(
    '/rating/overall',
    //validateRatingQuery,
    ReviewController.getOverallRating
);

// Get multiple rating summaries (public)
router.get(
    '/rating/multiple',
    validateMultipleRatingQuery,
    ReviewController.getMultipleRatings
);

// Get review by ID (public)
router.get('/:reviewId', ReviewController.getReviewById);

// Update review (customers only - own reviews)
router.put(
    '/:reviewId',
    validateReviewUpdate,
    fetchAuth.fetchAuthAllDataMiddleware,
    ReviewController.updateReview
);

// Delete review (customers can delete own, admins can delete any)
router.delete(
    '/:reviewId',
    fetchAuth.fetchAuthAllDataMiddleware,
    ReviewController.deleteReview
);

// Add provider response (providers only)
router.post(
    '/:reviewId/response',
    validateProviderResponse,
    fetchAuth.fetchAuthProviderDataMiddleware,
    ReviewController.addProviderResponse
);

// Add helpful vote (authenticated users)
router.post(
    '/:reviewId/vote',
    validateHelpfulVote,
    fetchAuth.fetchAuthAllDataMiddleware,
    ReviewController.addHelpfulVote
);

// Moderate review (admins only)
router.patch(
    '/:reviewId/moderate',
    validateReviewModeration,
    fetchAuth.fetchAuthAdminDataMiddleware,
    ReviewController.moderateReview
);

module.exports = router;
