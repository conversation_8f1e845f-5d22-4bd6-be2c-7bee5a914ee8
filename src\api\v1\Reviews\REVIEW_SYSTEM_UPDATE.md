# Review System Update - Frontend Integration

## Overview
The review system has been updated to match the frontend requirements with category ratings, image names array, and enhanced validation.

## ✅ Updated Data Structure

### Frontend Payload Structure
```json
{
  "providerId": "PROV_123456",
  "serviceId": "SRV_789012", 
  "bookingId": "BKG_345678",
  
  "qualityRating": 4,
  "timelinessRating": 3,
  "communicationRating": 4,
  "valueRating": 3,
  
  "title": "Excellent service quality",
  "comment": "The service was outstanding with great attention to detail...",
  "date": "2024-01-15T10:30:00.000Z",
  
  "imageNames": [
    "review-image-1-20240115.jpg",
    "review-image-2-20240115.jpg"
  ]
}
```

### Key Changes Made

#### 1. **Category Ratings (1-4 Scale)**
- `qualityRating`: Service quality rating
- `timelinessRating`: Timeliness rating  
- `communicationRating`: Communication rating
- `valueRating`: Value for money rating
- `overallRating`: Calculated automatically from category ratings

#### 2. **Image Handling**
- **New**: `imageNames` array - Simple string array of S3 file names
- **Legacy**: `images` array - Complex objects with URLs and captions (still supported)

#### 3. **Required Fields**
- All 4 category ratings are required
- `title` is now required (was optional)
- `comment` remains required
- `providerId`, `serviceId`, `bookingId` remain required

#### 4. **Optional Fields**
- `date`: Custom review date (defaults to current date)
- `imageNames`: Array of image file names (max 5)
- `overallRating`: Auto-calculated if not provided

## 🔧 Technical Implementation

### Database Schema Updates
```javascript
// New fields added to reviewSchema
qualityRating: { type: Number, required: true, min: 1, max: 4 },
timelinessRating: { type: Number, required: true, min: 1, max: 4 },
communicationRating: { type: Number, required: true, min: 1, max: 4 },
valueRating: { type: Number, required: true, min: 1, max: 4 },
overallRating: { type: Number, min: 1, max: 4 },
imageNames: [{ type: String, trim: true, maxlength: 255 }]
```

### Validation Rules
- **Category Ratings**: Integer between 1-4, all required
- **Title**: Required, 1-100 characters
- **Comment**: Required, 10-2000 characters  
- **Image Names**: Optional array, max 5 items, non-empty strings
- **Date**: Optional ISO 8601 date format

### Auto-Calculations
- **Overall Rating**: Average of 4 category ratings (rounded to 1 decimal)
- **Legacy Rating**: Converted to 1-5 scale for backward compatibility

## 🧪 Testing

### Run Tests
```bash
# Unit tests
npm test src/api/v1/Reviews/test/reviewSystem.test.js

# Integration test
node src/api/v1/Reviews/test/testReviewAPI.js
```

### Test Scenarios Covered
1. ✅ Create review with category ratings
2. ✅ Validate required fields
3. ✅ Validate rating ranges (1-4)
4. ✅ Image array validation (max 5)
5. ✅ Duplicate review prevention
6. ✅ Review retrieval with filtering

## 📡 API Endpoints

### Create Review
```http
POST /api/v1/reviews
Authorization: Bearer <token>
Content-Type: application/json

{
  "providerId": "PROV_123456",
  "serviceId": "SRV_789012",
  "bookingId": "BKG_345678",
  "qualityRating": 4,
  "timelinessRating": 3,
  "communicationRating": 4,
  "valueRating": 3,
  "title": "Great service",
  "comment": "Excellent work quality and communication...",
  "imageNames": ["image1.jpg", "image2.jpg"]
}
```

### Get Reviews
```http
GET /api/v1/reviews?serviceId=SRV_789012&providerId=PROV_123456
```

## 🔄 Backward Compatibility

The system maintains backward compatibility:
- Legacy `rating` field (1-5 scale) still supported
- Legacy `images` array format still works
- Existing reviews continue to function
- New reviews auto-populate legacy fields

## 🚀 Frontend Integration

### Expected Response Structure
```json
{
  "success": true,
  "message": "Review created successfully",
  "review": {
    "reviewId": "REV_000001",
    "qualityRating": 4,
    "timelinessRating": 3,
    "communicationRating": 4,
    "valueRating": 3,
    "overallRating": 3.5,
    "title": "Great service",
    "comment": "Excellent work...",
    "imageNames": ["image1.jpg", "image2.jpg"],
    "providerId": "PROV_123456",
    "serviceId": "SRV_789012",
    "status": "approved",
    "reviewDate": "2024-01-15T10:30:00.000Z"
  }
}
```

### Error Handling
```json
{
  "success": false,
  "errors": [
    {
      "field": "qualityRating",
      "message": "qualityRating is required."
    }
  ],
  "errorType": "VALIDATION_ERROR"
}
```

## 📝 Next Steps

1. **Test Integration**: Run the provided test scripts
2. **Frontend Testing**: Verify frontend can send/receive the new structure
3. **S3 Integration**: Ensure image upload returns proper `imageName` values
4. **Monitoring**: Monitor for any validation errors in production

## 🔍 Verification Checklist

- [ ] Category ratings validation (1-4 scale)
- [ ] Image names array handling
- [ ] Overall rating calculation
- [ ] Required field validation
- [ ] Backward compatibility
- [ ] Error response format
- [ ] Authentication integration
- [ ] Database storage verification

The review system is now ready to handle the complete frontend payload structure with category ratings and S3 image names!
